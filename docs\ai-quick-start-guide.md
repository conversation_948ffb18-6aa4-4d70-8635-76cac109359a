# AI快速开始指南 - 构建中转API

> 本指南为AI助手提供快速构建中转API的简化步骤和代码模板。

## 5分钟快速搭建

### 1. 项目初始化
```bash
mkdir my-proxy-api
cd my-proxy-api
npm init -y
npm install express axios form-data
```

### 2. 创建基础结构
```bash
mkdir routes docs
touch server.js routes/index.js routes/example.js
```

### 3. 复制核心代码

#### server.js (主服务器)
```javascript
const express = require('express');
const routes = require('./routes');
const app = express();

app.use(express.json({ limit: '10mb' }));
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  if (req.method === 'OPTIONS') return res.sendStatus(200);
  next();
});

app.use('/', routes);

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`🚀 服务启动: http://localhost:${PORT}`));
```

#### routes/index.js (动态路由管理器)
```javascript
const express = require('express');
const fs = require('fs');
const router = express.Router();

// 自动注册路由
const routeFiles = fs.readdirSync(__dirname)
  .filter(file => file.endsWith('.js') && file !== 'index.js');

const routes = {};
routeFiles.forEach(file => {
  const name = file.replace('.js', '');
  const module = require(`./${file}`);
  router.use(`/${name}`, module);
  routes[name] = `/${name}/v1/chat/completions`;
  console.log(`📍 注册路由: /${name}`);
});

// 统一接口
router.post('/v1/chat/completions', (req, res, next) => {
  const { model } = req.body;
  if (!model) return res.status(400).json({ error: '缺少model参数' });
  
  // 查找匹配的路由
  const routeName = Object.keys(routes).find(name => 
    model.toLowerCase().includes(name.toLowerCase())
  );
  
  if (routeName) {
    req.url = '/v1/chat/completions';
    require(`./${routeName}`)(req, res, next);
  } else {
    res.status(400).json({ 
      error: '不支持的模型', 
      supportedModels: Object.keys(routes) 
    });
  }
});

// 管理接口
router.get('/health', (req, res) => res.json({ status: 'ok', routes }));
router.get('/info', (req, res) => res.json({ 
  name: '中转API服务', 
  routes,
  usage: '修改routes/目录下的文件名即可调整API路径'
}));

module.exports = router;
```

### 4. 创建服务路由模板

#### routes/example.js (示例服务)
```javascript
const express = require('express');
const axios = require('axios');
const router = express.Router();

router.post('/v1/chat/completions', async (req, res) => {
  try {
    const { model, messages, stream } = req.body;
    const apiKey = req.headers.authorization;
    
    // TODO: 根据你的目标API调整以下代码
    
    // 1. 提取请求数据
    const userMessage = messages[messages.length - 1].content;
    
    // 2. 调用目标API
    const response = await axios.post('https://your-target-api.com/endpoint', {
      // 根据目标API格式调整
      input: userMessage,
      // 其他参数...
    }, {
      headers: { 
        'Authorization': apiKey,
        'Content-Type': 'application/json'
      },
      timeout: 20000
    });
    
    // 3. 转换响应格式为Chat Completions
    const result = response.data;
    
    if (stream) {
      // 流式响应
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      res.write(`data: ${JSON.stringify({
        id: `chatcmpl-${Date.now()}`,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: model,
        choices: [{
          index: 0,
          delta: { role: 'assistant', content: result.output },
          finish_reason: 'stop'
        }]
      })}\n\n`);
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      // 标准响应
      res.json({
        id: `chatcmpl-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: model,
        choices: [{
          index: 0,
          finish_reason: 'stop',
          message: {
            role: 'assistant',
            content: result.output // 根据目标API响应调整
          }
        }],
        usage: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      });
    }
    
  } catch (error) {
    console.error('处理失败:', error.message);
    res.status(500).json({ 
      error: '处理失败', 
      message: error.message 
    });
  }
});

module.exports = router;
```

### 5. 启动服务
```bash
node server.js
```

## 常用适配模板

### 文本生成API适配
```javascript
// 目标API: OpenAI风格
const response = await axios.post(targetUrl, {
  model: 'gpt-3.5-turbo',
  messages: messages,
  stream: stream
}, { headers: { Authorization: apiKey } });

// 直接返回（格式已兼容）
res.json(response.data);
```

### 图片处理API适配
```javascript
// 提取图片数据
const imageUrl = messages[messages.length - 1].content
  .find(item => item.type === 'image_url')?.image_url?.url;

const base64Data = imageUrl.replace(/^data:image\/\w+;base64,/, '');
const imgBuffer = Buffer.from(base64Data, 'base64');

// 发送到目标API
const form = new FormData();
form.append('image', imgBuffer, 'image.png');
form.append('task', 'remove_background');

const response = await axios.post(targetUrl, form, {
  headers: { ...form.getHeaders(), Authorization: apiKey }
});

// 返回图片结果
const resultImageUrl = response.data.result_url;
res.json({
  // ... Chat Completions格式
  choices: [{
    message: {
      role: 'assistant',
      content: `![处理结果](${resultImageUrl})`
    }
  }]
});
```

### 多模态API适配
```javascript
// 分离文本和图片
let textContent = '';
let imageContent = '';

const lastMessage = messages[messages.length - 1];
if (Array.isArray(lastMessage.content)) {
  lastMessage.content.forEach(item => {
    if (item.type === 'text') textContent = item.text;
    if (item.type === 'image_url') imageContent = item.image_url.url;
  });
} else {
  textContent = lastMessage.content;
}

// 调用目标API
const response = await axios.post(targetUrl, {
  text: textContent,
  image: imageContent,
  task: 'analyze'
});
```

## 快速定制指南

### 1. 修改服务名称
- 重命名 `routes/example.js` 为 `routes/yourservice.js`
- 路径自动变为 `/yourservice/v1/chat/completions`

### 2. 调整目标API
- 修改 `axios.post()` 的URL和参数
- 调整请求头和认证方式
- 适配响应数据格式

### 3. 添加新服务
- 在 `routes/` 目录创建新的 `.js` 文件
- 复制模板代码并修改
- 重启服务自动注册

### 4. 测试API
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "yourservice",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

## 常见问题解决

### 1. CORS错误
确保server.js中包含CORS中间件

### 2. 请求体过大
调整 `express.json({ limit: '50mb' })`

### 3. 超时问题
设置 `axios` 的 `timeout` 参数

### 4. 认证失败
检查 `Authorization` 头的传递

### 5. 路由不生效
确保文件名正确，重启服务

这个快速指南让您在5分钟内搭建一个功能完整的中转API服务！
