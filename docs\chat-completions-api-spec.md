# Chat Completions API 规范

> 本文档定义了Chat Completions API的标准格式，用于中转API的输入输出规范。

## 请求格式

### 基本结构
```json
{
  "model": "string",
  "messages": [
    {
      "role": "system|user|assistant",
      "content": "string|array"
    }
  ],
  "stream": false,
  "temperature": 1.0,
  "max_tokens": null,
  "top_p": 1.0,
  "frequency_penalty": 0.0,
  "presence_penalty": 0.0
}
```

### 参数说明

#### 必需参数
- **model** (string): 模型名称，用于路由到对应的处理器
- **messages** (array): 消息数组，包含对话历史

#### 可选参数
- **stream** (boolean): 是否使用流式响应，默认false
- **temperature** (number): 随机性控制，0-2之间，默认1.0
- **max_tokens** (integer): 最大生成token数，默认null（无限制）
- **top_p** (number): 核采样参数，0-1之间，默认1.0
- **frequency_penalty** (number): 频率惩罚，-2.0到2.0，默认0.0
- **presence_penalty** (number): 存在惩罚，-2.0到2.0，默认0.0

### 消息格式

#### 文本消息
```json
{
  "role": "user",
  "content": "Hello, how are you?"
}
```

#### 多模态消息
```json
{
  "role": "user",
  "content": [
    {
      "type": "text",
      "text": "What's in this image?"
    },
    {
      "type": "image_url",
      "image_url": {
        "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
      }
    }
  ]
}
```

#### 角色类型
- **system**: 系统提示，设置AI行为
- **user**: 用户输入
- **assistant**: AI回复

## 响应格式

### 标准响应
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking."
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### 流式响应
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"role":"assistant","content":""},"logprobs":null,"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"Hello"},"logprobs":null,"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{"content":"!"},"logprobs":null,"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"gpt-3.5-turbo","choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}

data: [DONE]
```

### 错误响应
```json
{
  "error": {
    "message": "Invalid request: missing required parameter 'model'",
    "type": "invalid_request_error",
    "param": "model",
    "code": "missing_required_parameter"
  }
}
```

## 字段详解

### 响应字段
- **id**: 唯一标识符，格式通常为 `chatcmpl-{timestamp}`
- **object**: 对象类型，标准响应为 `chat.completion`，流式为 `chat.completion.chunk`
- **created**: Unix时间戳
- **model**: 使用的模型名称
- **choices**: 生成的选择数组
- **usage**: token使用统计

### choices字段
- **index**: 选择索引，通常为0
- **message**: 完整消息对象（标准响应）
- **delta**: 增量消息对象（流式响应）
- **logprobs**: 对数概率信息，通常为null
- **finish_reason**: 结束原因

### finish_reason值
- **stop**: 自然结束
- **length**: 达到最大长度限制
- **function_call**: 调用函数
- **content_filter**: 内容过滤
- **null**: 未结束（流式响应中）

## 实现模板

### 标准响应生成
```javascript
function createChatCompletion(content, model = 'default') {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      message: {
        role: 'assistant',
        content: content
      },
      logprobs: null,
      finish_reason: 'stop'
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}
```

### 流式响应生成
```javascript
function createStreamChunk(content, model, isFirst = false, isLast = false) {
  const chunk = {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      delta: {},
      logprobs: null,
      finish_reason: isLast ? 'stop' : null
    }]
  };

  if (isFirst) {
    chunk.choices[0].delta.role = 'assistant';
    chunk.choices[0].delta.content = '';
  }
  
  if (content && !isLast) {
    chunk.choices[0].delta.content = content;
  }

  return chunk;
}

// 使用示例
res.write(`data: ${JSON.stringify(createStreamChunk('', model, true))}\n\n`);
res.write(`data: ${JSON.stringify(createStreamChunk('Hello', model))}\n\n`);
res.write(`data: ${JSON.stringify(createStreamChunk('', model, false, true))}\n\n`);
res.write('data: [DONE]\n\n');
```

### 错误响应生成
```javascript
function createErrorResponse(message, type = 'invalid_request_error', param = null) {
  return {
    error: {
      message: message,
      type: type,
      param: param,
      code: type.replace('_error', '')
    }
  };
}
```

## 特殊内容格式

### 图片内容
```json
{
  "role": "assistant",
  "content": "![图片描述](https://example.com/image.jpg)"
}
```

### 代码内容
```json
{
  "role": "assistant",
  "content": "```python\nprint('Hello, World!')\n```"
}
```

### 结构化数据
```json
{
  "role": "assistant",
  "content": "```json\n{\"result\": \"success\", \"data\": {...}}\n```"
}
```

## 验证规则

### 请求验证
```javascript
function validateRequest(req) {
  const { model, messages } = req.body;
  
  if (!model) {
    throw new Error('缺少必需参数: model');
  }
  
  if (!messages || !Array.isArray(messages)) {
    throw new Error('messages必须是数组');
  }
  
  if (messages.length === 0) {
    throw new Error('messages不能为空');
  }
  
  messages.forEach((msg, index) => {
    if (!msg.role || !['system', 'user', 'assistant'].includes(msg.role)) {
      throw new Error(`消息${index}: role必须是system、user或assistant`);
    }
    
    if (!msg.content) {
      throw new Error(`消息${index}: content不能为空`);
    }
  });
}
```

### 响应验证
```javascript
function validateResponse(response) {
  const required = ['id', 'object', 'created', 'model', 'choices'];
  
  required.forEach(field => {
    if (!(field in response)) {
      throw new Error(`响应缺少必需字段: ${field}`);
    }
  });
  
  if (!Array.isArray(response.choices) || response.choices.length === 0) {
    throw new Error('choices必须是非空数组');
  }
}
```

这个规范确保了中转API的标准化和兼容性，便于与各种客户端集成。
