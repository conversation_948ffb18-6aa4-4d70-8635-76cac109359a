const express = require('express');
const fs = require('fs');
const path = require('path');
const routes = require('./routes');

const app = express();

// 获取已注册的路由信息
function getRegisteredRoutes() {
  const routesDir = path.join(__dirname, 'routes');
  return fs.readdirSync(routesDir)
    .filter(file => file.endsWith('.js') && file !== 'index.js')
    .map(file => {
      const routeName = file.replace('.js', '');
      return {
        name: routeName,
        path: `/${routeName}`,
        endpoint: `/${routeName}/v1/chat/completions`
      };
    });
}

// 中间件配置
app.use(express.json({ limit: '10mb' }));

// CORS 支持
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-title');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toLocaleString('zh-CN');
  const clientName = req.headers['x-title'] || 'Unknown Client';
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${clientName}`);
  next();
});

// 注册路由
app.use('/', routes);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '接口不存在',
    message: `路径 ${req.originalUrl} 不存在`,
    availableEndpoints: [
      'POST /v1/chat/completions',
      'POST /rmbg/v1/chat/completions',
      'GET /health',
      'GET /info'
    ]
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err.message);
  res.status(500).json({
    error: '服务器内部错误',
    message: err.message
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  const registeredRoutes = getRegisteredRoutes();

  console.log(`🚀 AI 模型中转服务已启动`);
  console.log(`📡 服务端口: ${PORT}`);
  console.log(`🌐 服务地址: http://localhost:${PORT}`);
  console.log(`📋 API 信息: http://localhost:${PORT}/info`);
  console.log(`❤️  健康检查: http://localhost:${PORT}/health`);
  console.log(`\n已注册的路由:`);

  registeredRoutes.forEach(route => {
    console.log(`  • ${route.name.toUpperCase()}: http://localhost:${PORT}${route.endpoint}`);
  });

  console.log(`  • 统一接口: http://localhost:${PORT}/v1/chat/completions`);
  console.log(`\n使用说明:`);
  console.log(`  - 发送包含图片的 Chat Completions 格式请求`);
  console.log(`  - 修改 routes/ 目录下的文件名即可自动调整请求路径`);
  console.log(`  - 例如：将 matting.js 重命名为 rmbg.js，路径自动变为 /rmbg/v1/chat/completions`);
});
