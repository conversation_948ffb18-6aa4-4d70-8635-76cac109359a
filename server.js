const express = require('express');
const routes = require('./routes');

const app = express();

// 中间件配置
app.use(express.json({ limit: '10mb' }));

// CORS 支持
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-title');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toLocaleString('zh-CN');
  const clientName = req.headers['x-title'] || 'Unknown Client';
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${clientName}`);
  next();
});

// 注册路由
app.use('/', routes);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '接口不存在',
    message: `路径 ${req.originalUrl} 不存在`,
    availableEndpoints: [
      'POST /v1/chat/completions',
      'POST /rmbg/v1/chat/completions',
      'GET /health',
      'GET /info'
    ]
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err.message);
  res.status(500).json({
    error: '服务器内部错误',
    message: err.message
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Gitee AI 抠图中转服务已启动`);
  console.log(`📡 服务端口: ${PORT}`);
  console.log(`🌐 服务地址: http://localhost:${PORT}`);
  console.log(`📋 API 信息: http://localhost:${PORT}/info`);
  console.log(`❤️  健康检查: http://localhost:${PORT}/health`);
  console.log(`\n支持的功能:`);
  console.log(`  • RMBG-2.0 抠图: http://localhost:${PORT}/rmbg/v1/chat/completions`);
  console.log(`  • 统一接口: http://localhost:${PORT}/v1/chat/completions`);
  console.log(`\n使用说明:`);
  console.log(`  - 发送包含图片的 Chat Completions 格式请求`);
  console.log(`  - model 参数支持: "RMBG-2.0" 或包含 "rmbg"、"matting" 的字符串`);
});
