# 动态路由系统使用说明

## 概述

本项目实现了基于文件名的动态路由系统，您只需要修改 `routes/` 目录下的文件名，就能自动调整 API 请求路径，无需修改任何代码。

## 工作原理

1. **自动扫描**：服务启动时自动扫描 `routes/` 目录下的所有 `.js` 文件（除了 `index.js`）
2. **动态注册**：根据文件名自动注册对应的路由路径
3. **智能路由**：统一接口 `/v1/chat/completions` 根据 `model` 参数自动路由到对应处理器

## 使用方法

### 1. 修改路径

只需重命名 `routes/` 目录下的文件：

```bash
# 当前文件：routes/rmbg.js
# 对应路径：/rmbg/v1/chat/completions

# 重命名为：routes/matting.js  
# 新路径：/matting/v1/chat/completions

# 重命名为：routes/remove-bg.js
# 新路径：/remove-bg/v1/chat/completions
```

### 2. 访问方式

#### 直接访问
```bash
curl -X POST http://localhost:3000/{文件名}/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0",
    "messages": [...]
  }'
```

#### 统一接口（推荐）
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "rmbg",  # 或者文件名相关的任何字符串
    "messages": [...]
  }'
```

### 3. 模型参数匹配规则

统一接口会根据以下规则匹配路由：

- **精确匹配**：`model` 参数与文件名完全一致
- **包含匹配**：`model` 参数包含文件名，或文件名包含 `model` 参数
- **特殊匹配**：
  - `rmbg` 文件支持 `matting`、`remove` 等关键词
  - 大小写不敏感

#### 示例

文件名：`rmbg.js`
支持的 model 参数：
- `"RMBG-2.0"`
- `"rmbg"`
- `"matting"`
- `"remove"`
- `"background-removal"`

## 管理接口

### 健康检查
```bash
GET /health
```

返回当前已注册的所有路由信息。

### API 信息
```bash
GET /info
```

返回详细的 API 信息，包括所有可用端点和使用说明。

## 添加新的 API

1. **创建路由文件**：在 `routes/` 目录下创建新的 `.js` 文件
2. **实现路由逻辑**：参考 `rmbg.js` 的格式实现您的 API 逻辑
3. **重启服务**：重启服务后自动注册新路由

### 路由文件模板

```javascript
const express = require('express');
const router = express.Router();

// 您的 API 逻辑
router.post('/v1/chat/completions', async (req, res) => {
  try {
    // 处理请求
    const { model, messages } = req.body;
    
    // 您的业务逻辑
    
    // 返回 Chat Completions 格式响应
    res.json({
      id: `completion-${Date.now()}`,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: model,
      choices: [{
        index: 0,
        finish_reason: 'stop',
        message: {
          role: 'assistant',
          content: '处理结果'
        }
      }]
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

## 优势

1. **零配置**：无需修改配置文件或代码
2. **即时生效**：重命名文件后重启服务即可
3. **灵活性**：支持任意文件名和路径
4. **向后兼容**：保持原有功能不变
5. **易于管理**：通过文件系统直观管理 API 路径

## 注意事项

1. **文件名限制**：文件名应符合 URL 路径规范，避免特殊字符
2. **重启要求**：修改文件名后需要重启服务才能生效
3. **唯一性**：确保文件名唯一，避免路径冲突
4. **模块导出**：确保路由文件正确导出 Express Router 对象
