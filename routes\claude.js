const express = require('express');
const router = express.Router();

// <PERSON> 路由
router.post('/v1/chat/completions', async (req, res) => {
  try {
    // TODO: 实现 Claude 接口逻辑
    res.status(501).json({
      error: '<PERSON> 接口暂未实现',
      message: '该功能正在开发中'
    });
  } catch (err) {
    console.error('❌ Claude 处理失败:', err.message);
    res.status(500).json({ error: err.message });
  }
});

module.exports = router;
