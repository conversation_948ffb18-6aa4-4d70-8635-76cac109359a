# 项目结构说明

## 目录架构

```
zhongzhuan/
├── package.json          # 项目配置和依赖
├── .gitignore            # Git 忽略文件
├── README.md             # 项目说明文档
├── server.js             # 主服务器入口文件
├── routes/               # 路由目录
│   ├── index.js          # 路由管理和统一入口
│   ├── rmbg.js           # 抠图模型路由 (RMBG-2.0)
│   ├── midjourney.js     # 图像生成路由 (Midjourney)
│   ├── sd.js             # Stable Diffusion 路由
│   ├── gpt4.js           # GPT-4 模型路由
│   ├── claude.js         # Claude 模型路由
│   └── yi.js             # Yi 模型路由
└── test-api.js           # API 测试文件

```

## 各文件功能说明

### 主要文件

- **server.js**: 主服务器入口文件，负责启动服务和基础配置
- **package.json**: 项目配置，主入口已更新为 `server.js`

### 路由文件

- **routes/index.js**: 路由管理中心
  - 统一的 `/v1/chat/completions` 接口，根据 `model` 参数自动路由
  - 健康检查接口 `/health`
  - API 信息接口 `/info`

- **routes/rmbg.js**: RMBG-2.0 抠图路由
  - 原 `index.js` 的功能，已重命名为 RMBG-2.0
  - 处理图片抠图请求

- **其他模型路由**: 为未来扩展预留的路由文件
  - `midjourney.js`: Midjourney 接口
  - `sd.js`: Stable Diffusion 接口  
  - `gpt4.js`: GPT-4 接口
  - `claude.js`: Claude 接口
  - `yi.js`: Yi 接口

## API 接口

### 统一接口
- `POST /v1/chat/completions` - 根据 model 参数自动路由到对应处理器

### 专用接口
- `POST /rmbg/v1/chat/completions` - RMBG-2.0 抠图接口
- `POST /midjourney/v1/chat/completions` - Midjourney 接口
- `POST /sd/v1/chat/completions` - Stable Diffusion 接口
- `POST /gpt4/v1/chat/completions` - GPT-4 接口
- `POST /claude/v1/chat/completions` - Claude 接口
- `POST /yi/v1/chat/completions` - Yi 接口

### 管理接口
- `GET /health` - 健康检查
- `GET /info` - API 信息

## 启动方式

```bash
# 启动服务
npm start

# 或者
node server.js
```

## 支持的模型

目前完全实现的模型：
- **RMBG-2.0**: 图片抠图功能

待实现的模型：
- Midjourney: 图像生成
- Stable Diffusion: 图像生成  
- GPT-4: 文本生成
- Claude: 文本生成
- Yi: 文本生成

## 客户端访问示例

### 使用统一接口（推荐）
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0",
    "messages": [...]
  }'
```

### 使用专用接口
```bash
curl -X POST http://localhost:3000/rmbg/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0", 
    "messages": [...]
  }'
```

## 架构优势

1. **模块化设计**: 每个模型独立路由文件，便于维护
2. **统一接口**: 支持根据模型参数自动路由
3. **易于扩展**: 新增模型只需添加对应路由文件
4. **清晰结构**: 代码组织清晰，职责分明
5. **向后兼容**: 保持原有 RMBG-2.0 功能不变
