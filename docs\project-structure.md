# 项目结构说明

## 目录架构

```
zhongzhuan/
├── package.json          # 项目配置和依赖
├── .gitignore            # Git 忽略文件
├── README.md             # 项目说明文档
├── server.js             # 主服务器入口文件
├── routes/               # 路由目录
│   ├── index.js          # 路由管理和统一入口
│   └── rmbg.js           # RMBG-2.0 抠图路由
└── project-structure.md  # 项目结构说明文档

```

## 各文件功能说明

### 主要文件

- **server.js**: 主服务器入口文件，负责启动服务和基础配置
- **package.json**: 项目配置，主入口已更新为 `server.js`

### 路由文件

- **routes/index.js**: 路由管理中心
  - 统一的 `/v1/chat/completions` 接口，根据 `model` 参数路由到 RMBG
  - 健康检查接口 `/health`
  - API 信息接口 `/info`

- **routes/rmbg.js**: RMBG-2.0 抠图路由
  - 处理图片抠图请求
  - 支持 Chat Completions 格式的输入输出
  - 调用 Gitee AI 抠图接口

## API 接口

### 主要接口
- `POST /v1/chat/completions` - 统一抠图接口（根据 model 参数路由）
- `POST /rmbg/v1/chat/completions` - RMBG-2.0 抠图接口（直接访问）

### 管理接口
- `GET /health` - 健康检查
- `GET /info` - API 信息

## 启动方式

```bash
# 启动服务
npm start

# 或者
node server.js
```

## 支持的模型

目前支持的模型：
- **RMBG-2.0**: 图片背景移除/抠图功能
  - 支持多种图片格式
  - 返回处理后的图片（URL 或 base64 格式）
  - 兼容 Chat Completions 格式

## 客户端访问示例

### 使用统一接口（推荐）
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
            }
          }
        ]
      }
    ]
  }'
```

### 使用专用接口
```bash
curl -X POST http://localhost:3000/rmbg/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
            }
          }
        ]
      }
    ]
  }'
```

## 架构优势

1. **专注功能**: 专门针对 RMBG-2.0 抠图功能优化
2. **统一接口**: 支持 Chat Completions 格式，便于集成
3. **简洁高效**: 去除冗余代码，提高性能
4. **易于维护**: 代码结构清晰，功能单一
5. **兼容性好**: 支持多种访问方式和图片格式
