# 项目结构说明

## 目录架构

```
zhongzhuan/
├── package.json          # 项目配置和依赖
├── .gitignore            # Git 忽略文件
├── README.md             # 项目说明文档
├── server.js             # 主服务器入口文件
├── routes/               # 路由目录
│   ├── index.js          # 路由管理和统一入口
│   └── rmbg.js           # RMBG-2.0 抠图路由
└── project-structure.md  # 项目结构说明文档

```

## 各文件功能说明

### 主要文件

- **server.js**: 主服务器入口文件，负责启动服务和基础配置
- **package.json**: 项目配置，主入口已更新为 `server.js`

### 路由文件

- **routes/index.js**: 路由管理中心
  - 统一的 `/v1/chat/completions` 接口，根据 `model` 参数路由到 RMBG
  - 健康检查接口 `/health`
  - API 信息接口 `/info`

- **routes/rmbg.js**: RMBG-2.0 抠图路由
  - 处理图片抠图请求
  - 支持 Chat Completions 格式的输入输出
  - 调用 Gitee AI 抠图接口

## API 接口

### 主要接口
- `POST /v1/chat/completions` - 统一抠图接口（根据 model 参数路由）
- `POST /rmbg/v1/chat/completions` - RMBG-2.0 抠图接口（直接访问）

### 管理接口
- `GET /health` - 健康检查
- `GET /info` - API 信息

## 启动方式

```bash
# 启动服务
npm start

# 或者
node server.js
```

## 支持的模型

目前支持的模型：
- **RMBG-2.0**: 图片背景移除/抠图功能
  - 支持多种图片格式
  - 返回处理后的图片（URL 或 base64 格式）
  - 兼容 Chat Completions 格式

## 客户端访问示例

### 使用统一接口（推荐）
```bash
curl -X POST http://localhost:3000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
            }
          }
        ]
      }
    ]
  }'
```

### 使用专用接口
```bash
curl -X POST http://localhost:3000/rmbg/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "RMBG-2.0",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
            }
          }
        ]
      }
    ]
  }'
```

## 架构优势

1. **动态路由**: 基于文件名自动注册路由，修改文件名即可调整 API 路径
2. **零配置**: 无需修改配置文件，重命名文件即可改变请求路径
3. **统一接口**: 支持 Chat Completions 格式，便于集成
4. **易于扩展**: 添加新 API 只需创建新的路由文件
5. **智能匹配**: 统一接口根据 model 参数自动路由到对应处理器
6. **简洁高效**: 代码结构清晰，性能优化

## 动态路由特性

- **文件名即路径**: `rmbg.js` → `/rmbg/v1/chat/completions`
- **自动发现**: 启动时自动扫描 `routes/` 目录
- **热重载**: 重启服务后新路由自动生效
- **智能路由**: 根据 model 参数自动匹配对应路由

## 相关文档

- **[动态路由系统使用说明](./dynamic-routing.md)** - 详细的动态路由使用指南
- **[AI中转API构建教程](./ai-tutorial-building-proxy-api.md)** - 完整的中转API开发教程（AI专用）
- **[AI快速开始指南](./ai-quick-start-guide.md)** - 5分钟快速搭建指南（AI专用）
- **[Chat Completions API规范](./chat-completions-api-spec.md)** - 标准API格式规范
