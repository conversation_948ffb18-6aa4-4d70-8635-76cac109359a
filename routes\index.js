const express = require('express');
const router = express.Router();

// 导入 RMBG 路由
const rmbgRouter = require('./rmbg');

// 注册 RMBG 路由
router.use('/rmbg', rmbgRouter);

// 根路径处理 - 直接路由到 RMBG 处理器
router.post('/v1/chat/completions', (req, res, next) => {
  const { model } = req.body;

  if (!model) {
    return res.status(400).json({
      error: '缺少 model 参数',
      message: '请在请求体中指定模型名称'
    });
  }

  // 检查是否为支持的抠图模型
  if (model.toLowerCase().includes('rmbg') || model.toLowerCase().includes('matting')) {
    // 转发到 RMBG 路由处理器
    req.url = '/v1/chat/completions';
    rmbgRouter(req, res, next);
  } else {
    return res.status(400).json({
      error: '不支持的模型',
      message: `模型 "${model}" 暂不支持，当前只支持 RMBG-2.0 抠图模型`,
      supportedModels: ['RMBG-2.0']
    });
  }
});

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    availableRoutes: ['/rmbg'],
    supportedModels: ['RMBG-2.0']
  });
});

// API 信息接口
router.get('/info', (req, res) => {
  res.json({
    name: 'Gitee AI 抠图中转服务',
    version: '1.0.0',
    description: 'Gitee AI 抠图接口的 Chat Completions 格式中转服务',
    endpoints: {
      'POST /v1/chat/completions': 'RMBG-2.0 抠图接口（根据 model 参数路由）',
      'POST /rmbg/v1/chat/completions': 'RMBG-2.0 抠图接口（直接访问）',
      'GET /health': '健康检查',
      'GET /info': 'API 信息'
    },
    supportedModels: ['RMBG-2.0'],
    modelDescription: {
      'RMBG-2.0': '图片背景移除/抠图功能，支持多种图片格式'
    }
  });
});

module.exports = router;
