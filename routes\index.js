const express = require('express');
const router = express.Router();

// 导入各个模型的路由
const rmbgRouter = require('./rmbg');
const midjourneyRouter = require('./midjourney');
const sdRouter = require('./sd');
const gpt4Router = require('./gpt4');
const claudeRouter = require('./claude');
const yiRouter = require('./yi');

// 路由映射配置
const routeConfig = {
  '/rmbg': rmbgRouter,
  '/midjourney': midjourneyRouter,
  '/sd': sdRouter,
  '/gpt4': gpt4Router,
  '/claude': claudeRouter,
  '/yi': yiRouter
};

// 注册所有路由
Object.entries(routeConfig).forEach(([path, routerInstance]) => {
  router.use(path, routerInstance);
});

// 根路径处理 - 根据请求体中的模型参数路由到对应的处理器
router.post('/v1/chat/completions', (req, res, next) => {
  const { model } = req.body;
  
  if (!model) {
    return res.status(400).json({ 
      error: '缺少 model 参数',
      message: '请在请求体中指定模型名称'
    });
  }

  // 根据模型名称路由到对应的处理器
  let targetPath = '';
  
  if (model.toLowerCase().includes('rmbg') || model.toLowerCase().includes('matting')) {
    targetPath = '/rmbg';
  } else if (model.toLowerCase().includes('midjourney')) {
    targetPath = '/midjourney';
  } else if (model.toLowerCase().includes('stable') || model.toLowerCase().includes('sd')) {
    targetPath = '/sd';
  } else if (model.toLowerCase().includes('gpt-4') || model.toLowerCase().includes('gpt4')) {
    targetPath = '/gpt4';
  } else if (model.toLowerCase().includes('claude')) {
    targetPath = '/claude';
  } else if (model.toLowerCase().includes('yi')) {
    targetPath = '/yi';
  } else {
    return res.status(400).json({
      error: '不支持的模型',
      message: `模型 "${model}" 暂不支持，支持的模型: RMBG-2.0, Midjourney, Stable Diffusion, GPT-4, Claude, Yi`,
      supportedModels: ['RMBG-2.0', 'Midjourney', 'Stable Diffusion', 'GPT-4', 'Claude', 'Yi']
    });
  }

  // 转发到对应的路由处理器
  req.url = '/v1/chat/completions';
  routeConfig[targetPath](req, res, next);
});

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    availableRoutes: Object.keys(routeConfig),
    supportedModels: ['RMBG-2.0', 'Midjourney', 'Stable Diffusion', 'GPT-4', 'Claude', 'Yi']
  });
});

// API 信息接口
router.get('/info', (req, res) => {
  res.json({
    name: 'AI 模型中转服务',
    version: '1.0.0',
    description: '支持多种 AI 模型的统一 Chat Completions 格式接口',
    endpoints: {
      'POST /v1/chat/completions': '统一的聊天完成接口，根据 model 参数自动路由',
      'POST /rmbg/v1/chat/completions': 'RMBG-2.0 抠图接口',
      'POST /midjourney/v1/chat/completions': 'Midjourney 接口',
      'POST /sd/v1/chat/completions': 'Stable Diffusion 接口',
      'POST /gpt4/v1/chat/completions': 'GPT-4 接口',
      'POST /claude/v1/chat/completions': 'Claude 接口',
      'POST /yi/v1/chat/completions': 'Yi 接口',
      'GET /health': '健康检查',
      'GET /info': 'API 信息'
    },
    supportedModels: ['RMBG-2.0', 'Midjourney', 'Stable Diffusion', 'GPT-4', 'Claude', 'Yi']
  });
});

module.exports = router;
