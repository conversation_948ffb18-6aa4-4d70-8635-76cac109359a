const express = require('express');
const fs = require('fs');
const path = require('path');
const router = express.Router();

// 自动扫描并注册路由
const routesDir = __dirname;
const routeFiles = fs.readdirSync(routesDir)
  .filter(file => file.endsWith('.js') && file !== 'index.js')
  .map(file => file.replace('.js', ''));

// 存储路由信息
const registeredRoutes = {};
const supportedModels = [];

// 动态注册路由
routeFiles.forEach(routeName => {
  try {
    const routeModule = require(`./${routeName}`);
    const routePath = `/${routeName}`;

    // 注册路由
    router.use(routePath, routeModule);

    // 记录路由信息
    registeredRoutes[routePath] = {
      name: routeName,
      path: routePath,
      endpoint: `${routePath}/v1/chat/completions`
    };

    // 添加到支持的模型列表（根据文件名推断）
    const modelName = routeName.toUpperCase();
    supportedModels.push(modelName);

    console.log(`📍 已注册路由: ${routePath} -> ${routeName}.js`);
  } catch (error) {
    console.error(`❌ 注册路由失败: ${routeName}.js -`, error.message);
  }
});

// 统一接口处理 - 根据 model 参数自动路由到对应处理器
router.post('/v1/chat/completions', (req, res, next) => {
  const { model } = req.body;

  if (!model) {
    return res.status(400).json({
      error: '缺少 model 参数',
      message: '请在请求体中指定模型名称'
    });
  }

  // 根据模型名称查找对应的路由
  let targetRoute = null;
  const modelLower = model.toLowerCase();

  // 遍历已注册的路由，查找匹配的模型
  for (const [routePath, routeInfo] of Object.entries(registeredRoutes)) {
    const routeName = routeInfo.name.toLowerCase();

    // 检查模型名称是否包含路由名称，或者路由名称包含模型关键词
    if (modelLower.includes(routeName) ||
        (routeName === 'rmbg' && (modelLower.includes('matting') || modelLower.includes('remove'))) ||
        modelLower === routeName) {
      targetRoute = routeInfo;
      break;
    }
  }

  if (targetRoute) {
    // 转发到对应的路由处理器
    req.url = '/v1/chat/completions';
    const routeModule = require(`./${targetRoute.name}`);
    routeModule(req, res, next);
  } else {
    return res.status(400).json({
      error: '不支持的模型',
      message: `模型 "${model}" 暂不支持`,
      supportedModels: supportedModels,
      availableRoutes: Object.keys(registeredRoutes)
    });
  }
});

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    availableRoutes: Object.keys(registeredRoutes),
    supportedModels: supportedModels,
    registeredRoutes: registeredRoutes
  });
});

// API 信息接口
router.get('/info', (req, res) => {
  // 动态生成端点信息
  const endpoints = {
    'POST /v1/chat/completions': '统一接口（根据 model 参数自动路由）',
    'GET /health': '健康检查',
    'GET /info': 'API 信息'
  };

  // 添加动态注册的路由端点
  Object.values(registeredRoutes).forEach(route => {
    endpoints[`POST ${route.endpoint}`] = `${route.name.toUpperCase()} 接口（直接访问）`;
  });

  res.json({
    name: 'AI 模型中转服务',
    version: '1.0.0',
    description: '基于文件名自动注册路由的 Chat Completions 格式中转服务',
    endpoints: endpoints,
    supportedModels: supportedModels,
    registeredRoutes: registeredRoutes,
    usage: {
      note: '修改 routes/ 目录下的文件名即可自动调整请求路径',
      example: '将 rmbg.js 重命名为 matting.js，路径自动变为 /matting/v1/chat/completions'
    }
  });
});

module.exports = router;
