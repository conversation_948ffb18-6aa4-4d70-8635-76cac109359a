# AI 中转API构建教程

> 本文档专为AI助手编写，提供构建中转API的完整指南和最佳实践。

## 概述

中转API是一种代理服务，用于将一种API格式转换为另一种格式，或者为现有API提供统一的访问接口。本项目实现了将Gitee AI抠图接口包装为Chat Completions格式的中转服务。

## 核心架构

### 1. 项目结构
```
project/
├── server.js              # 主服务器入口
├── routes/                 # 路由目录
│   ├── index.js           # 路由管理器（动态路由系统）
│   └── {service}.js       # 具体服务路由（如rmbg.js）
├── package.json           # 项目配置
└── docs/                  # 文档目录
```

### 2. 技术栈
- **Node.js + Express**: 服务器框架
- **axios**: HTTP客户端
- **form-data**: 处理multipart/form-data
- **动态路由系统**: 基于文件名自动注册路由

## 实现步骤

### 步骤1: 创建主服务器 (server.js)

```javascript
const express = require('express');
const fs = require('fs');
const path = require('path');
const routes = require('./routes');

const app = express();

// 中间件配置
app.use(express.json({ limit: '10mb' }));

// CORS支持
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-title');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toLocaleString('zh-CN');
  const clientName = req.headers['x-title'] || 'Unknown Client';
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${clientName}`);
  next();
});

// 注册路由
app.use('/', routes);

// 错误处理
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err.message);
  res.status(500).json({
    error: '服务器内部错误',
    message: err.message
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 中转服务已启动，端口: ${PORT}`);
});
```

### 步骤2: 实现动态路由系统 (routes/index.js)

```javascript
const express = require('express');
const fs = require('fs');
const router = express.Router();

// 自动扫描并注册路由
const routesDir = __dirname;
const routeFiles = fs.readdirSync(routesDir)
  .filter(file => file.endsWith('.js') && file !== 'index.js')
  .map(file => file.replace('.js', ''));

const registeredRoutes = {};
const supportedModels = [];

// 动态注册路由
routeFiles.forEach(routeName => {
  try {
    const routeModule = require(`./${routeName}`);
    const routePath = `/${routeName}`;
    
    router.use(routePath, routeModule);
    
    registeredRoutes[routePath] = {
      name: routeName,
      path: routePath,
      endpoint: `${routePath}/v1/chat/completions`
    };
    
    supportedModels.push(routeName.toUpperCase());
    console.log(`📍 已注册路由: ${routePath} -> ${routeName}.js`);
  } catch (error) {
    console.error(`❌ 注册路由失败: ${routeName}.js -`, error.message);
  }
});

// 统一接口 - 智能路由
router.post('/v1/chat/completions', (req, res, next) => {
  const { model } = req.body;

  if (!model) {
    return res.status(400).json({
      error: '缺少 model 参数',
      message: '请在请求体中指定模型名称'
    });
  }

  // 智能匹配路由
  let targetRoute = null;
  const modelLower = model.toLowerCase();

  for (const [routePath, routeInfo] of Object.entries(registeredRoutes)) {
    const routeName = routeInfo.name.toLowerCase();
    
    if (modelLower.includes(routeName) || 
        modelLower === routeName ||
        (routeName === 'rmbg' && (modelLower.includes('matting') || modelLower.includes('remove')))) {
      targetRoute = routeInfo;
      break;
    }
  }

  if (targetRoute) {
    req.url = '/v1/chat/completions';
    const routeModule = require(`./${targetRoute.name}`);
    routeModule(req, res, next);
  } else {
    return res.status(400).json({
      error: '不支持的模型',
      message: `模型 "${model}" 暂不支持`,
      supportedModels: supportedModels
    });
  }
});

module.exports = router;
```

### 步骤3: 创建具体服务路由 (routes/{service}.js)

以RMBG抠图服务为例：

```javascript
const express = require('express');
const axios = require('axios');
const FormData = require('form-data');

const router = express.Router();

router.post('/v1/chat/completions', async (req, res) => {
  try {
    // 1. 解析请求参数
    const { model, messages, stream } = req.body;
    const apiKey = req.headers['authorization'] || '';
    
    // 2. 提取图片数据
    const lastMessage = messages[messages.length - 1];
    let imageData = '';
    
    if (lastMessage && Array.isArray(lastMessage.content)) {
      const imageContents = lastMessage.content.filter(item => item.type === 'image_url');
      if (imageContents.length > 0) {
        imageData = imageContents[imageContents.length - 1].image_url.url;
      }
    }

    if (!imageData) {
      return res.status(400).json({ 
        error: '缺少图片数据',
        message: '请在消息中包含图片'
      });
    }

    // 3. 构造目标API请求
    const form = new FormData();
    form.append('model', 'RMBG-2.0');
    form.append('response_format', 'url');
    
    const base64Data = imageData.replace(/^data:image\/\w+;base64,/, '');
    const imgBuffer = Buffer.from(base64Data, 'base64');
    form.append('image', imgBuffer, { filename: 'image.png', contentType: 'image/png' });

    // 4. 调用目标API
    const response = await axios.post(
      'https://ai.gitee.com/v1/images/mattings',
      form,
      {
        headers: {
          ...form.getHeaders(),
          Authorization: apiKey,
        },
        timeout: 20000,
      }
    );

    // 5. 转换响应格式
    const result = response.data?.data?.[0];
    const imageResult = result?.url || `data:image/png;base64,${result?.b64_json}`;
    
    // 6. 返回Chat Completions格式响应
    if (stream) {
      // 流式响应处理
      res.setHeader('Content-Type', 'text/plain; charset=utf-8');
      // ... 流式响应逻辑
    } else {
      // 标准响应
      res.json({
        id: `completion-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: 'RMBG-2.0',
        choices: [{
          index: 0,
          finish_reason: 'stop',
          message: {
            role: 'assistant',
            content: `![处理结果](${imageResult})`
          }
        }],
        usage: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      });
    }

  } catch (error) {
    console.error('处理失败:', error.message);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
```

## 关键设计模式

### 1. 动态路由注册
- 自动扫描routes目录
- 基于文件名生成路由路径
- 支持热重载（重启后生效）

### 2. 智能模型匹配
- 支持精确匹配、包含匹配
- 特殊关键词映射
- 大小写不敏感

### 3. 统一响应格式
- 标准Chat Completions格式
- 支持流式和非流式响应
- 错误处理标准化

### 4. 中间件架构
- 请求日志记录
- CORS支持
- 错误处理
- 请求体大小限制

## 最佳实践

### 1. 错误处理
```javascript
try {
  // API调用逻辑
} catch (error) {
  console.error('处理失败:', error.message);
  
  if (error.response) {
    // 目标API错误
    res.status(error.response.status).json({
      error: '目标API调用失败',
      details: error.response.data
    });
  } else {
    // 系统错误
    res.status(500).json({ error: error.message });
  }
}
```

### 2. 请求验证
```javascript
// 参数验证
if (!model || !messages) {
  return res.status(400).json({
    error: '缺少必要参数',
    required: ['model', 'messages']
  });
}

// 数据格式验证
if (!Array.isArray(messages)) {
  return res.status(400).json({
    error: '消息格式错误',
    message: 'messages必须是数组'
  });
}
```

### 3. 日志记录
```javascript
// 简洁的请求日志
const timestamp = new Date().toLocaleString('zh-CN');
const clientName = req.headers['x-title'] || 'Unknown Client';
console.log(`[${timestamp}] ${clientName} 发起${serviceName}请求`);

// 处理结果日志
console.log(`✅ ${serviceName}处理成功`);
console.log(`❌ ${serviceName}处理失败:`, error.message);
```

## 扩展指南

### 添加新服务
1. 在routes目录创建新的.js文件
2. 实现Express Router
3. 导出router对象
4. 重启服务自动注册

### 文件命名规范
- 使用小写字母和连字符
- 避免特殊字符
- 确保文件名唯一
- 文件名即API路径

### 配置管理
- 使用环境变量存储敏感信息
- 支持配置文件覆盖
- 提供默认配置

## 常见场景和解决方案

### 场景1: 文本生成API中转
```javascript
// routes/gpt.js
router.post('/v1/chat/completions', async (req, res) => {
  const { messages, model, stream } = req.body;

  // 转换为目标API格式
  const targetRequest = {
    model: 'gpt-3.5-turbo',
    messages: messages,
    stream: stream
  };

  const response = await axios.post(
    'https://api.openai.com/v1/chat/completions',
    targetRequest,
    { headers: { Authorization: req.headers.authorization } }
  );

  // 直接返回或转换格式
  res.json(response.data);
});
```

### 场景2: 图像生成API中转
```javascript
// routes/dalle.js
router.post('/v1/chat/completions', async (req, res) => {
  const { messages } = req.body;
  const prompt = messages[messages.length - 1].content;

  // 调用图像生成API
  const response = await axios.post(
    'https://api.openai.com/v1/images/generations',
    {
      prompt: prompt,
      n: 1,
      size: "1024x1024"
    },
    { headers: { Authorization: req.headers.authorization } }
  );

  // 转换为Chat Completions格式
  const imageUrl = response.data.data[0].url;
  res.json({
    id: `img-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model: 'dall-e-3',
    choices: [{
      index: 0,
      finish_reason: 'stop',
      message: {
        role: 'assistant',
        content: `![生成的图片](${imageUrl})`
      }
    }]
  });
});
```

### 场景3: 多模态API中转
```javascript
// routes/vision.js
router.post('/v1/chat/completions', async (req, res) => {
  const { messages } = req.body;

  // 提取文本和图片
  const lastMessage = messages[messages.length - 1];
  let textContent = '';
  let imageContent = '';

  if (Array.isArray(lastMessage.content)) {
    lastMessage.content.forEach(item => {
      if (item.type === 'text') textContent = item.text;
      if (item.type === 'image_url') imageContent = item.image_url.url;
    });
  }

  // 调用视觉理解API
  const response = await axios.post(targetApiUrl, {
    text: textContent,
    image: imageContent
  });

  // 返回标准格式
  res.json(formatChatCompletionResponse(response.data));
});
```

## 高级特性实现

### 1. 流式响应处理
```javascript
// 流式响应模板
if (stream) {
  res.setHeader('Content-Type', 'text/plain; charset=utf-8');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');

  const streamId = `chatcmpl-${Date.now()}`;

  // 发送数据块
  const sendChunk = (data) => {
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  };

  // 开始块
  sendChunk({
    id: streamId,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      delta: { role: 'assistant', content: '' },
      finish_reason: null
    }]
  });

  // 内容块
  sendChunk({
    id: streamId,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      delta: { content: responseContent },
      finish_reason: null
    }]
  });

  // 结束块
  sendChunk({
    id: streamId,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model: model,
    choices: [{
      index: 0,
      delta: {},
      finish_reason: 'stop'
    }]
  });

  res.write('data: [DONE]\n\n');
  res.end();
}
```

### 2. 请求重试机制
```javascript
const retryRequest = async (requestFn, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;

      const delay = Math.pow(2, i) * 1000; // 指数退避
      await new Promise(resolve => setTimeout(resolve, delay));
      console.log(`重试第 ${i + 1} 次...`);
    }
  }
};

// 使用示例
const response = await retryRequest(() =>
  axios.post(targetApiUrl, requestData, config)
);
```

### 3. 请求缓存
```javascript
const cache = new Map();

const getCacheKey = (req) => {
  return JSON.stringify({
    url: req.url,
    body: req.body,
    headers: req.headers.authorization
  });
};

router.post('/v1/chat/completions', async (req, res) => {
  const cacheKey = getCacheKey(req);

  // 检查缓存
  if (cache.has(cacheKey)) {
    console.log('返回缓存结果');
    return res.json(cache.get(cacheKey));
  }

  // 处理请求
  const result = await processRequest(req);

  // 存储缓存（设置过期时间）
  cache.set(cacheKey, result);
  setTimeout(() => cache.delete(cacheKey), 5 * 60 * 1000); // 5分钟过期

  res.json(result);
});
```

## 部署和运维

### 1. Docker部署
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm install --production

COPY . .

EXPOSE 3000
CMD ["node", "server.js"]
```

### 2. 环境配置
```javascript
// config.js
module.exports = {
  port: process.env.PORT || 3000,
  apiTimeout: process.env.API_TIMEOUT || 20000,
  maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
  logLevel: process.env.LOG_LEVEL || 'info'
};
```

### 3. 健康检查
```javascript
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: require('../package.json').version
  });
});
```

## 测试和调试

### 1. 单元测试示例
```javascript
// test/routes.test.js
const request = require('supertest');
const app = require('../server');

describe('API Routes', () => {
  test('POST /v1/chat/completions', async () => {
    const response = await request(app)
      .post('/v1/chat/completions')
      .send({
        model: 'test-model',
        messages: [{ role: 'user', content: 'Hello' }]
      });

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('choices');
  });
});
```

### 2. 调试技巧
```javascript
// 详细的错误日志
const logError = (error, context) => {
  console.error('=== 错误详情 ===');
  console.error('时间:', new Date().toISOString());
  console.error('上下文:', context);
  console.error('错误信息:', error.message);
  console.error('堆栈:', error.stack);
  if (error.response) {
    console.error('响应状态:', error.response.status);
    console.error('响应数据:', error.response.data);
  }
  console.error('================');
};
```

这个架构提供了高度的灵活性和可扩展性，适合快速构建各种API中转服务。通过动态路由系统，您可以轻松添加新的服务，只需创建对应的路由文件即可。
